
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.11.0
 * Query Engine version: 9c30299f5a0ea26a96790e13f796dc6094db3173
 */
Prisma.prismaVersion = {
  client: "6.11.0",
  engine: "9c30299f5a0ea26a96790e13f796dc6094db3173"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  emailVerified: 'emailVerified',
  image: 'image',
  password: 'password',
  role: 'role',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AccountScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  provider: 'provider',
  providerAccountId: 'providerAccountId',
  refresh_token: 'refresh_token',
  access_token: 'access_token',
  expires_at: 'expires_at',
  token_type: 'token_type',
  scope: 'scope',
  id_token: 'id_token',
  session_state: 'session_state'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  sessionToken: 'sessionToken',
  userId: 'userId',
  expires: 'expires'
};

exports.Prisma.VerificationTokenScalarFieldEnum = {
  identifier: 'identifier',
  token: 'token',
  expires: 'expires'
};

exports.Prisma.UserPreferenceScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  theme: 'theme',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrganizationScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  image: 'image',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrganizationMemberScalarFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  userId: 'userId',
  role: 'role',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrganizationInvitationScalarFieldEnum = {
  id: 'id',
  email: 'email',
  organizationId: 'organizationId',
  role: 'role',
  token: 'token',
  expires: 'expires',
  createdAt: 'createdAt'
};

exports.Prisma.ProjectScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  organizationId: 'organizationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TaskScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  status: 'status',
  priority: 'priority',
  projectId: 'projectId',
  assigneeId: 'assigneeId',
  dueDate: 'dueDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WorkspaceScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  projectId: 'projectId',
  type: 'type',
  status: 'status',
  config: 'config',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WebVMInstanceScalarFieldEnum = {
  id: 'id',
  name: 'name',
  workspaceId: 'workspaceId',
  status: 'status',
  imageUrl: 'imageUrl',
  config: 'config',
  resources: 'resources',
  networkConfig: 'networkConfig',
  connectionUrl: 'connectionUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  startedAt: 'startedAt',
  stoppedAt: 'stoppedAt',
  templateId: 'templateId'
};

exports.Prisma.FirecrackerVMScalarFieldEnum = {
  id: 'id',
  name: 'name',
  workspaceId: 'workspaceId',
  status: 'status',
  vmId: 'vmId',
  config: 'config',
  resources: 'resources',
  networkConfig: 'networkConfig',
  kernelImage: 'kernelImage',
  rootfsImage: 'rootfsImage',
  socketPath: 'socketPath',
  logPath: 'logPath',
  metricsPath: 'metricsPath',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  startedAt: 'startedAt',
  stoppedAt: 'stoppedAt',
  templateId: 'templateId'
};

exports.Prisma.FirecrackerContainerScalarFieldEnum = {
  id: 'id',
  vmId: 'vmId',
  name: 'name',
  image: 'image',
  status: 'status',
  config: 'config',
  ports: 'ports',
  volumes: 'volumes',
  environment: 'environment',
  command: 'command',
  workingDir: 'workingDir',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  startedAt: 'startedAt',
  stoppedAt: 'stoppedAt'
};

exports.Prisma.FirecrackerMetricScalarFieldEnum = {
  id: 'id',
  vmId: 'vmId',
  metricType: 'metricType',
  value: 'value',
  unit: 'unit',
  timestamp: 'timestamp'
};

exports.Prisma.FirecrackerSnapshotScalarFieldEnum = {
  id: 'id',
  vmId: 'vmId',
  name: 'name',
  description: 'description',
  snapshotPath: 'snapshotPath',
  memoryPath: 'memoryPath',
  size: 'size',
  createdAt: 'createdAt'
};

exports.Prisma.FirecrackerTemplateScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  category: 'category',
  kernelImage: 'kernelImage',
  rootfsImage: 'rootfsImage',
  config: 'config',
  resources: 'resources',
  isPublic: 'isPublic',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FirecrackerContainerLogScalarFieldEnum = {
  id: 'id',
  containerId: 'containerId',
  timestamp: 'timestamp',
  level: 'level',
  message: 'message',
  source: 'source'
};

exports.Prisma.WebVMMetricScalarFieldEnum = {
  id: 'id',
  instanceId: 'instanceId',
  metricType: 'metricType',
  value: 'value',
  unit: 'unit',
  timestamp: 'timestamp'
};

exports.Prisma.StorageBucketScalarFieldEnum = {
  id: 'id',
  name: 'name',
  region: 'region',
  versioning: 'versioning',
  encryption: 'encryption',
  size: 'size',
  objectCount: 'objectCount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StorageObjectScalarFieldEnum = {
  id: 'id',
  key: 'key',
  bucketId: 'bucketId',
  size: 'size',
  contentType: 'contentType',
  etag: 'etag',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StorageUsageScalarFieldEnum = {
  id: 'id',
  bucketId: 'bucketId',
  totalSize: 'totalSize',
  objectCount: 'objectCount',
  quota: 'quota',
  timestamp: 'timestamp'
};

exports.Prisma.ContainerScalarFieldEnum = {
  id: 'id',
  name: 'name',
  image: 'image',
  status: 'status',
  command: 'command',
  environment: 'environment',
  ports: 'ports',
  volumes: 'volumes',
  labels: 'labels',
  restartPolicy: 'restartPolicy',
  networkMode: 'networkMode',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  startedAt: 'startedAt',
  stoppedAt: 'stoppedAt'
};

exports.Prisma.ContainerStatsScalarFieldEnum = {
  id: 'id',
  containerId: 'containerId',
  cpuPercent: 'cpuPercent',
  memoryUsage: 'memoryUsage',
  memoryLimit: 'memoryLimit',
  networkRx: 'networkRx',
  networkTx: 'networkTx',
  blockRead: 'blockRead',
  blockWrite: 'blockWrite',
  timestamp: 'timestamp'
};

exports.Prisma.ContainerLogScalarFieldEnum = {
  id: 'id',
  containerId: 'containerId',
  message: 'message',
  level: 'level',
  timestamp: 'timestamp'
};

exports.Prisma.ServiceHealthScalarFieldEnum = {
  id: 'id',
  serviceName: 'serviceName',
  status: 'status',
  responseTime: 'responseTime',
  error: 'error',
  details: 'details',
  lastCheck: 'lastCheck',
  createdAt: 'createdAt'
};

exports.Prisma.InfrastructureMetricScalarFieldEnum = {
  id: 'id',
  service: 'service',
  metricName: 'metricName',
  value: 'value',
  unit: 'unit',
  labels: 'labels',
  timestamp: 'timestamp'
};

exports.Prisma.AuditLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  action: 'action',
  resource: 'resource',
  resourceId: 'resourceId',
  details: 'details',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  timestamp: 'timestamp'
};

exports.Prisma.WebVMTemplateScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  image: 'image',
  memory: 'memory',
  cpuCount: 'cpuCount',
  diskSize: 'diskSize',
  preInstalledSoftware: 'preInstalledSoftware',
  category: 'category',
  isPublic: 'isPublic',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WebVMSnapshotScalarFieldEnum = {
  id: 'id',
  instanceId: 'instanceId',
  name: 'name',
  description: 'description',
  size: 'size',
  createdAt: 'createdAt'
};

exports.Prisma.WorkspacePersistenceScalarFieldEnum = {
  id: 'id',
  workspaceId: 'workspaceId',
  storageSize: 'storageSize',
  backupCount: 'backupCount',
  lastBackup: 'lastBackup',
  config: 'config',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InfrastructureAlertScalarFieldEnum = {
  id: 'id',
  name: 'name',
  query: 'query',
  condition: 'condition',
  threshold: 'threshold',
  duration: 'duration',
  severity: 'severity',
  state: 'state',
  labels: 'labels',
  annotations: 'annotations',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  firedAt: 'firedAt',
  resolvedAt: 'resolvedAt'
};

exports.Prisma.AlertNotificationScalarFieldEnum = {
  id: 'id',
  alertId: 'alertId',
  type: 'type',
  recipient: 'recipient',
  message: 'message',
  sentAt: 'sentAt'
};

exports.Prisma.WorkspaceFileScalarFieldEnum = {
  id: 'id',
  workspaceId: 'workspaceId',
  path: 'path',
  name: 'name',
  type: 'type',
  size: 'size',
  mimeType: 'mimeType',
  encoding: 'encoding',
  content: 'content',
  hash: 'hash',
  parentId: 'parentId',
  isDirectory: 'isDirectory',
  permissions: 'permissions',
  metadata: 'metadata',
  version: 'version',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastAccessedAt: 'lastAccessedAt'
};

exports.Prisma.FileIndexScalarFieldEnum = {
  id: 'id',
  workspaceId: 'workspaceId',
  path: 'path',
  content: 'content',
  tokens: 'tokens',
  language: 'language',
  symbols: 'symbols',
  imports: 'imports',
  exports: 'exports',
  dependencies: 'dependencies',
  complexity: 'complexity',
  lastIndexed: 'lastIndexed',
  indexVersion: 'indexVersion'
};

exports.Prisma.WorkspaceStateScalarFieldEnum = {
  id: 'id',
  workspaceId: 'workspaceId',
  sessionId: 'sessionId',
  environment: 'environment',
  processes: 'processes',
  openFiles: 'openFiles',
  terminalSessions: 'terminalSessions',
  editorState: 'editorState',
  gitState: 'gitState',
  installedPackages: 'installedPackages',
  customSettings: 'customSettings',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WorkspaceSyncScalarFieldEnum = {
  id: 'id',
  workspaceId: 'workspaceId',
  type: 'type',
  status: 'status',
  direction: 'direction',
  source: 'source',
  target: 'target',
  progress: 'progress',
  conflicts: 'conflicts',
  lastSync: 'lastSync',
  nextSync: 'nextSync',
  settings: 'settings',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  image: 'image',
  password: 'password'
};

exports.Prisma.AccountOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  provider: 'provider',
  providerAccountId: 'providerAccountId',
  refresh_token: 'refresh_token',
  access_token: 'access_token',
  token_type: 'token_type',
  scope: 'scope',
  id_token: 'id_token',
  session_state: 'session_state'
};

exports.Prisma.SessionOrderByRelevanceFieldEnum = {
  id: 'id',
  sessionToken: 'sessionToken',
  userId: 'userId'
};

exports.Prisma.VerificationTokenOrderByRelevanceFieldEnum = {
  identifier: 'identifier',
  token: 'token'
};

exports.Prisma.UserPreferenceOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  theme: 'theme'
};

exports.Prisma.OrganizationOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  image: 'image'
};

exports.Prisma.OrganizationMemberOrderByRelevanceFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  userId: 'userId'
};

exports.Prisma.OrganizationInvitationOrderByRelevanceFieldEnum = {
  id: 'id',
  email: 'email',
  organizationId: 'organizationId',
  token: 'token'
};

exports.Prisma.ProjectOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  organizationId: 'organizationId'
};

exports.Prisma.TaskOrderByRelevanceFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  projectId: 'projectId',
  assigneeId: 'assigneeId'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.WorkspaceOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  projectId: 'projectId'
};

exports.Prisma.WebVMInstanceOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  workspaceId: 'workspaceId',
  imageUrl: 'imageUrl',
  connectionUrl: 'connectionUrl',
  templateId: 'templateId'
};

exports.Prisma.FirecrackerVMOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  workspaceId: 'workspaceId',
  vmId: 'vmId',
  kernelImage: 'kernelImage',
  rootfsImage: 'rootfsImage',
  socketPath: 'socketPath',
  logPath: 'logPath',
  metricsPath: 'metricsPath',
  templateId: 'templateId'
};

exports.Prisma.FirecrackerContainerOrderByRelevanceFieldEnum = {
  id: 'id',
  vmId: 'vmId',
  name: 'name',
  image: 'image',
  command: 'command',
  workingDir: 'workingDir'
};

exports.Prisma.FirecrackerMetricOrderByRelevanceFieldEnum = {
  id: 'id',
  vmId: 'vmId',
  unit: 'unit'
};

exports.Prisma.FirecrackerSnapshotOrderByRelevanceFieldEnum = {
  id: 'id',
  vmId: 'vmId',
  name: 'name',
  description: 'description',
  snapshotPath: 'snapshotPath',
  memoryPath: 'memoryPath'
};

exports.Prisma.FirecrackerTemplateOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  category: 'category',
  kernelImage: 'kernelImage',
  rootfsImage: 'rootfsImage'
};

exports.Prisma.FirecrackerContainerLogOrderByRelevanceFieldEnum = {
  id: 'id',
  containerId: 'containerId',
  message: 'message',
  source: 'source'
};

exports.Prisma.WebVMMetricOrderByRelevanceFieldEnum = {
  id: 'id',
  instanceId: 'instanceId',
  unit: 'unit'
};

exports.Prisma.StorageBucketOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  region: 'region'
};

exports.Prisma.StorageObjectOrderByRelevanceFieldEnum = {
  id: 'id',
  key: 'key',
  bucketId: 'bucketId',
  contentType: 'contentType',
  etag: 'etag'
};

exports.Prisma.StorageUsageOrderByRelevanceFieldEnum = {
  id: 'id',
  bucketId: 'bucketId'
};

exports.Prisma.ContainerOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  image: 'image',
  restartPolicy: 'restartPolicy',
  networkMode: 'networkMode'
};

exports.Prisma.ContainerStatsOrderByRelevanceFieldEnum = {
  id: 'id',
  containerId: 'containerId'
};

exports.Prisma.ContainerLogOrderByRelevanceFieldEnum = {
  id: 'id',
  containerId: 'containerId',
  message: 'message'
};

exports.Prisma.ServiceHealthOrderByRelevanceFieldEnum = {
  id: 'id',
  serviceName: 'serviceName',
  error: 'error'
};

exports.Prisma.InfrastructureMetricOrderByRelevanceFieldEnum = {
  id: 'id',
  service: 'service',
  metricName: 'metricName',
  unit: 'unit'
};

exports.Prisma.AuditLogOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  action: 'action',
  resource: 'resource',
  resourceId: 'resourceId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent'
};

exports.Prisma.WebVMTemplateOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  image: 'image',
  memory: 'memory',
  diskSize: 'diskSize',
  category: 'category'
};

exports.Prisma.WebVMSnapshotOrderByRelevanceFieldEnum = {
  id: 'id',
  instanceId: 'instanceId',
  name: 'name',
  description: 'description'
};

exports.Prisma.WorkspacePersistenceOrderByRelevanceFieldEnum = {
  id: 'id',
  workspaceId: 'workspaceId'
};

exports.Prisma.InfrastructureAlertOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  query: 'query',
  condition: 'condition',
  duration: 'duration'
};

exports.Prisma.AlertNotificationOrderByRelevanceFieldEnum = {
  id: 'id',
  alertId: 'alertId',
  recipient: 'recipient',
  message: 'message'
};

exports.Prisma.WorkspaceFileOrderByRelevanceFieldEnum = {
  id: 'id',
  workspaceId: 'workspaceId',
  path: 'path',
  name: 'name',
  type: 'type',
  mimeType: 'mimeType',
  encoding: 'encoding',
  content: 'content',
  hash: 'hash',
  parentId: 'parentId'
};

exports.Prisma.FileIndexOrderByRelevanceFieldEnum = {
  id: 'id',
  workspaceId: 'workspaceId',
  path: 'path',
  content: 'content',
  language: 'language',
  indexVersion: 'indexVersion'
};

exports.Prisma.WorkspaceStateOrderByRelevanceFieldEnum = {
  id: 'id',
  workspaceId: 'workspaceId',
  sessionId: 'sessionId'
};

exports.Prisma.WorkspaceSyncOrderByRelevanceFieldEnum = {
  id: 'id',
  workspaceId: 'workspaceId',
  direction: 'direction'
};
exports.UserRole = exports.$Enums.UserRole = {
  USER: 'USER',
  ADMIN: 'ADMIN',
  SUPER_ADMIN: 'SUPER_ADMIN'
};

exports.MemberRole = exports.$Enums.MemberRole = {
  OWNER: 'OWNER',
  ADMIN: 'ADMIN',
  MEMBER: 'MEMBER'
};

exports.TaskStatus = exports.$Enums.TaskStatus = {
  TODO: 'TODO',
  IN_PROGRESS: 'IN_PROGRESS',
  DONE: 'DONE'
};

exports.TaskPriority = exports.$Enums.TaskPriority = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH'
};

exports.WorkspaceType = exports.$Enums.WorkspaceType = {
  WEBVM: 'WEBVM',
  FIRECRACKER: 'FIRECRACKER',
  PYODIDE: 'PYODIDE'
};

exports.WorkspaceStatus = exports.$Enums.WorkspaceStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  ARCHIVED: 'ARCHIVED',
  ERROR: 'ERROR'
};

exports.WebVMStatus = exports.$Enums.WebVMStatus = {
  STARTING: 'STARTING',
  RUNNING: 'RUNNING',
  STOPPING: 'STOPPING',
  STOPPED: 'STOPPED',
  ERROR: 'ERROR',
  SUSPENDED: 'SUSPENDED'
};

exports.FirecrackerStatus = exports.$Enums.FirecrackerStatus = {
  STARTING: 'STARTING',
  RUNNING: 'RUNNING',
  STOPPING: 'STOPPING',
  STOPPED: 'STOPPED',
  ERROR: 'ERROR',
  SUSPENDED: 'SUSPENDED',
  PAUSED: 'PAUSED'
};

exports.ContainerStatus = exports.$Enums.ContainerStatus = {
  CREATING: 'CREATING',
  RUNNING: 'RUNNING',
  STOPPED: 'STOPPED',
  ERROR: 'ERROR',
  DELETING: 'DELETING',
  UNKNOWN: 'UNKNOWN'
};

exports.MetricType = exports.$Enums.MetricType = {
  CPU_USAGE: 'CPU_USAGE',
  MEMORY_USAGE: 'MEMORY_USAGE',
  DISK_USAGE: 'DISK_USAGE',
  NETWORK_IN: 'NETWORK_IN',
  NETWORK_OUT: 'NETWORK_OUT',
  RESPONSE_TIME: 'RESPONSE_TIME'
};

exports.LogLevel = exports.$Enums.LogLevel = {
  DEBUG: 'DEBUG',
  INFO: 'INFO',
  WARN: 'WARN',
  ERROR: 'ERROR',
  FATAL: 'FATAL'
};

exports.HealthStatus = exports.$Enums.HealthStatus = {
  HEALTHY: 'HEALTHY',
  UNHEALTHY: 'UNHEALTHY',
  DEGRADED: 'DEGRADED',
  UNKNOWN: 'UNKNOWN'
};

exports.AlertSeverity = exports.$Enums.AlertSeverity = {
  CRITICAL: 'CRITICAL',
  WARNING: 'WARNING',
  INFO: 'INFO'
};

exports.AlertState = exports.$Enums.AlertState = {
  INACTIVE: 'INACTIVE',
  PENDING: 'PENDING',
  FIRING: 'FIRING',
  RESOLVED: 'RESOLVED'
};

exports.NotificationType = exports.$Enums.NotificationType = {
  EMAIL: 'EMAIL',
  SLACK: 'SLACK',
  WEBHOOK: 'WEBHOOK',
  SMS: 'SMS'
};

exports.SyncType = exports.$Enums.SyncType = {
  REALTIME: 'REALTIME',
  PERIODIC: 'PERIODIC',
  MANUAL: 'MANUAL',
  ONDEMAND: 'ONDEMAND'
};

exports.SyncStatus = exports.$Enums.SyncStatus = {
  IDLE: 'IDLE',
  SYNCING: 'SYNCING',
  CONFLICT: 'CONFLICT',
  ERROR: 'ERROR',
  PAUSED: 'PAUSED'
};

exports.Prisma.ModelName = {
  User: 'User',
  Account: 'Account',
  Session: 'Session',
  VerificationToken: 'VerificationToken',
  UserPreference: 'UserPreference',
  Organization: 'Organization',
  OrganizationMember: 'OrganizationMember',
  OrganizationInvitation: 'OrganizationInvitation',
  Project: 'Project',
  Task: 'Task',
  Workspace: 'Workspace',
  WebVMInstance: 'WebVMInstance',
  FirecrackerVM: 'FirecrackerVM',
  FirecrackerContainer: 'FirecrackerContainer',
  FirecrackerMetric: 'FirecrackerMetric',
  FirecrackerSnapshot: 'FirecrackerSnapshot',
  FirecrackerTemplate: 'FirecrackerTemplate',
  FirecrackerContainerLog: 'FirecrackerContainerLog',
  WebVMMetric: 'WebVMMetric',
  StorageBucket: 'StorageBucket',
  StorageObject: 'StorageObject',
  StorageUsage: 'StorageUsage',
  Container: 'Container',
  ContainerStats: 'ContainerStats',
  ContainerLog: 'ContainerLog',
  ServiceHealth: 'ServiceHealth',
  InfrastructureMetric: 'InfrastructureMetric',
  AuditLog: 'AuditLog',
  WebVMTemplate: 'WebVMTemplate',
  WebVMSnapshot: 'WebVMSnapshot',
  WorkspacePersistence: 'WorkspacePersistence',
  InfrastructureAlert: 'InfrastructureAlert',
  AlertNotification: 'AlertNotification',
  WorkspaceFile: 'WorkspaceFile',
  FileIndex: 'FileIndex',
  WorkspaceState: 'WorkspaceState',
  WorkspaceSync: 'WorkspaceSync'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
