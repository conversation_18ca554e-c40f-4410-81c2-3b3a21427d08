/**
 * Pyodide Project File Loading Service
 * Handles loading project template files into Pyodide workspaces
 */

import { db } from '@/lib/db';
import { createHash } from 'crypto';
import { WorkspaceFileStorage } from '@/lib/workspace/services/file-storage';
import { WorkspaceFileIndexer } from '@/lib/workspace/services/file-indexer';

export interface ProjectFileLoadOptions {
  overwriteExisting?: boolean;
  createIndexes?: boolean;
  includeReadme?: boolean;
}

export interface LoadedFileInfo {
  id: string;
  path: string;
  name: string;
  type: string;
  size: number;
  content?: string;
  isDirectory: boolean;
  source: 'template' | 'starter' | 'existing';
}

export interface ProjectLoadResult {
  success: boolean;
  filesLoaded: LoadedFileInfo[];
  projectName?: string;
  error?: string;
}

export class PyodideProjectLoader {
  private workspaceId: string;
  private fileStorage: WorkspaceFileStorage;
  private fileIndexer: WorkspaceFileIndexer;

  constructor(workspaceId: string) {
    this.workspaceId = workspaceId;
    this.fileStorage = new WorkspaceFileStorage(workspaceId);
    this.fileIndexer = new WorkspaceFileIndexer(workspaceId);
  }

  /**
   * Load project files into the Pyodide workspace
   */
  async loadProjectFiles(
    projectId: string, 
    options: ProjectFileLoadOptions = {}
  ): Promise<ProjectLoadResult> {
    try {
      const {
        overwriteExisting = false,
        createIndexes = true,
        includeReadme = true
      } = options;

      // Get project information
      const project = await db.project.findUnique({
        where: { id: projectId },
        select: { id: true, name: true, description: true }
      });

      if (!project) {
        return {
          success: false,
          filesLoaded: [],
          error: 'Project not found'
        };
      }

      // Check if workspace already has files
      const existingFiles = await db.workspaceFile.findMany({
        where: { workspaceId: this.workspaceId },
        select: { id: true, path: true }
      });

      if (existingFiles.length > 0 && !overwriteExisting) {
        return {
          success: false,
          filesLoaded: [],
          error: 'Workspace already contains files. Use overwriteExisting option to replace them.'
        };
      }

      // Clear existing files if overwriting
      if (overwriteExisting && existingFiles.length > 0) {
        await db.workspaceFile.deleteMany({
          where: { workspaceId: this.workspaceId }
        });
      }

      const loadedFiles: LoadedFileInfo[] = [];

      // Try to find template files from other workspaces in the same project
      const templateFiles = await this.findProjectTemplateFiles(projectId);
      
      if (templateFiles.length > 0) {
        // Load template files
        for (const templateFile of templateFiles) {
          const loadedFile = await this.createWorkspaceFile({
            path: templateFile.path,
            name: templateFile.name,
            content: templateFile.content || '',
            isDirectory: templateFile.isDirectory,
            type: templateFile.type,
            mimeType: templateFile.mimeType,
            metadata: {
              ...templateFile.metadata,
              loadedFromProject: true,
              originalFileId: templateFile.id,
              loadedAt: new Date().toISOString()
            }
          });

          loadedFiles.push({
            ...loadedFile,
            source: 'template'
          });

          // Create file index if requested
          if (createIndexes && !templateFile.isDirectory && templateFile.content) {
            try {
              await this.fileIndexer.indexFile(loadedFile, templateFile.content);
            } catch (indexError) {
              console.warn('Failed to index file:', templateFile.path, indexError);
            }
          }
        }
      } else {
        // Create starter files if no templates exist
        const starterFiles = await this.createStarterFiles(project);
        loadedFiles.push(...starterFiles);

        // Index starter files
        if (createIndexes) {
          for (const file of starterFiles) {
            if (!file.isDirectory && file.content) {
              try {
                await this.fileIndexer.indexFile(file, file.content);
              } catch (indexError) {
                console.warn('Failed to index starter file:', file.path, indexError);
              }
            }
          }
        }
      }

      // Create README if requested and doesn't exist
      if (includeReadme && !loadedFiles.some(f => f.name.toLowerCase() === 'readme.md')) {
        const readmeFile = await this.createReadmeFile(project);
        loadedFiles.push(readmeFile);

        if (createIndexes && readmeFile.content) {
          try {
            await this.fileIndexer.indexFile(readmeFile, readmeFile.content);
          } catch (indexError) {
            console.warn('Failed to index README file:', indexError);
          }
        }
      }

      return {
        success: true,
        filesLoaded: loadedFiles,
        projectName: project.name
      };

    } catch (error) {
      console.error('Error loading project files:', error);
      return {
        success: false,
        filesLoaded: [],
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Find template files from other workspaces in the same project
   */
  private async findProjectTemplateFiles(projectId: string) {
    const projectWorkspaces = await db.workspace.findMany({
      where: {
        projectId: projectId,
        type: 'PYODIDE'
      },
      include: {
        files: {
          where: {
            OR: [
              {
                metadata: {
                  path: ['$.createdBy'],
                  equals: 'template'
                }
              },
              {
                metadata: {
                  path: ['$.source'],
                  equals: 'template'
                }
              }
            ]
          },
          orderBy: [
            { isDirectory: 'desc' },
            { path: 'asc' }
          ]
        }
      }
    });

    // Return files from the first workspace that has template files
    for (const workspace of projectWorkspaces) {
      if (workspace.files.length > 0) {
        return workspace.files;
      }
    }

    return [];
  }

  /**
   * Create starter files for a new project
   */
  private async createStarterFiles(project: { name: string; description?: string | null }): Promise<LoadedFileInfo[]> {
    const starterFiles: LoadedFileInfo[] = [];

    // Create main Python file
    const mainContent = `# ${project.name}
${project.description ? `# ${project.description}` : ''}

def main():
    """Main function for ${project.name}"""
    print("Welcome to ${project.name}!")
    print("Start building your Python project here.")
    
    # Your code goes here
    pass

if __name__ == "__main__":
    main()
`;

    const mainFile = await this.createWorkspaceFile({
      path: 'main.py',
      name: 'main.py',
      content: mainContent,
      isDirectory: false,
      type: 'PYTHON',
      mimeType: 'text/x-python',
      metadata: {
        language: 'python',
        createdBy: 'system',
        description: 'Main Python file',
        source: 'starter'
      }
    });

    starterFiles.push({
      ...mainFile,
      source: 'starter'
    });

    return starterFiles;
  }

  /**
   * Create README file for the project
   */
  private async createReadmeFile(project: { name: string; description?: string | null }): Promise<LoadedFileInfo> {
    const readmeContent = `# ${project.name}

${project.description || 'A Python project created with VibeKraft.'}

## Getting Started

This project is set up to run in a Pyodide environment. You can:

1. Edit the Python files in the file explorer
2. Run code in the terminal using the Python interpreter
3. Install packages using micropip

## Files

- \`main.py\` - Main application file
- \`README.md\` - This file

## Running the Code

To run the main file, use the terminal and execute:

\`\`\`python
exec(open('main.py').read())
\`\`\`

Or simply run individual Python statements in the terminal.

Happy coding!
`;

    const readmeFile = await this.createWorkspaceFile({
      path: 'README.md',
      name: 'README.md',
      content: readmeContent,
      isDirectory: false,
      type: 'TEXT',
      mimeType: 'text/markdown',
      metadata: {
        language: 'markdown',
        createdBy: 'system',
        description: 'Project documentation',
        source: 'starter'
      }
    });

    return {
      ...readmeFile,
      source: 'starter'
    };
  }

  /**
   * Create a workspace file record
   */
  private async createWorkspaceFile(fileData: {
    path: string;
    name: string;
    content: string;
    isDirectory: boolean;
    type: string;
    mimeType: string;
    metadata: any;
  }): Promise<LoadedFileInfo> {
    const hash = createHash('sha256').update(fileData.content).digest('hex');

    const file = await db.workspaceFile.create({
      data: {
        workspaceId: this.workspaceId,
        path: fileData.path,
        name: fileData.name,
        type: fileData.type,
        size: BigInt(Buffer.byteLength(fileData.content, 'utf8')),
        mimeType: fileData.mimeType,
        encoding: 'utf-8',
        content: fileData.content,
        hash,
        isDirectory: fileData.isDirectory,
        permissions: {
          read: true,
          write: true,
          execute: fileData.type === 'PYTHON'
        },
        metadata: fileData.metadata,
        version: 1,
        lastAccessedAt: new Date()
      }
    });

    return {
      id: file.id,
      path: file.path,
      name: file.name,
      type: file.type,
      size: Number(file.size),
      content: file.content || undefined,
      isDirectory: file.isDirectory,
      source: 'existing'
    };
  }
}
