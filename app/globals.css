@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
    --background: oklch(1.0000 0 0);
    --foreground: oklch(0.3211 0 0);
    --card: oklch(1.0000 0 0);
    --card-foreground: oklch(0.3211 0 0);
    --popover: oklch(1.0000 0 0);
    --popover-foreground: oklch(0.3211 0 0);
    --primary: oklch(0.5699 0.1271 238.3563);
    --primary-foreground: oklch(1.0000 0 0);
    --secondary: oklch(1.0000 0 0);
    --secondary-foreground: oklch(0.4781 0.0279 256.1871);
    --muted: oklch(1.0000 0 0);
    --muted-foreground: oklch(0.5912 0.0234 261.7272);
    --accent: oklch(0.9834 0.0042 236.4956);
    --accent-foreground: oklch(0.4050 0.1489 265.4014);
    --destructive: oklch(0.6750 0.1793 23.1830);
    --destructive-foreground: oklch(1.0000 0 0);
    --border: oklch(0.9697 0.0017 247.8393);
    --input: oklch(1.0000 0 0);
    --ring: oklch(0.6770 0.1511 36.8636);
    --chart-1: oklch(0.7711 0.0476 247.4458);
    --chart-2: oklch(0.8494 0.0621 35.8588);
    --chart-3: oklch(0.6218 0.0672 254.6138);
    --chart-4: oklch(0.5367 0.0921 259.5542);
    --chart-5: oklch(0.4537 0.1025 263.6606);
    --sidebar: oklch(0.9727 0.0017 247.8392);
    --sidebar-foreground: oklch(0.3211 0 0);
    --sidebar-primary: oklch(0.6770 0.1511 36.8636);
    --sidebar-primary-foreground: oklch(1.0000 0 0);
    --sidebar-accent: oklch(0.9834 0.0042 236.4956);
    --sidebar-accent-foreground: oklch(0.4050 0.1489 265.4014);
    --sidebar-border: oklch(1.0000 0 0);
    --sidebar-ring: oklch(0.6770 0.1511 36.8636);
    --font-sans: Outfit, sans-serif;
    --font-serif: Oxanium, sans-serif;
    --font-mono: JetBrains Mono, monospace;
    --radius: 0.45rem;
    --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.05);
    --shadow-xs: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.05);
    --shadow-sm: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.10), 0px 1px 2px -1px hsl(0 0% 10.1961% / 0.10);
    --shadow: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.10), 0px 1px 2px -1px hsl(0 0% 10.1961% / 0.10);
    --shadow-md: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.10), 0px 2px 4px -1px hsl(0 0% 10.1961% / 0.10);
    --shadow-lg: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.10), 0px 4px 6px -1px hsl(0 0% 10.1961% / 0.10);
    --shadow-xl: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.10), 0px 8px 10px -1px hsl(0 0% 10.1961% / 0.10);
    --shadow-2xl: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.25);
    --tracking-normal: 0em;
    --spacing: 0.2rem;
}

.dark {
    --background: oklch(0.2759 0.0325 261.6825);
    --foreground: oklch(0.9219 0 0);
    --card: oklch(0.3298 0.0317 267.2395);
    --card-foreground: oklch(0.9219 0 0);
    --popover: oklch(0.3070 0.0280 270.3414);
    --popover-foreground: oklch(0.9219 0 0);
    --primary: oklch(0.5699 0.1271 238.3563);
    --primary-foreground: oklch(1.0000 0 0);
    --secondary: oklch(0.3288 0.0284 265.2874);
    --secondary-foreground: oklch(0.9219 0 0);
    --muted: oklch(0.3288 0.0284 265.2874);
    --muted-foreground: oklch(0.7155 0 0);
    --accent: oklch(0.3587 0.0650 267.9772);
    --accent-foreground: oklch(0.9636 0.0176 253.3378);
    --destructive: oklch(0.6750 0.1793 23.1830);
    --destructive-foreground: oklch(1.0000 0 0);
    --border: oklch(0.4100 0.0316 268.3338);
    --input: oklch(0.4100 0.0316 268.3338);
    --ring: oklch(0.6770 0.1511 36.8636);
    --chart-1: oklch(0.7711 0.0476 247.4458);
    --chart-2: oklch(0.8312 0.0621 34.7041);
    --chart-3: oklch(0.6218 0.0672 254.6138);
    --chart-4: oklch(0.5367 0.0921 259.5542);
    --chart-5: oklch(0.4537 0.1025 263.6606);
    --sidebar: oklch(0.3293 0.0300 266.3247);
    --sidebar-foreground: oklch(0.9219 0 0);
    --sidebar-primary: oklch(0.6770 0.1511 36.8636);
    --sidebar-primary-foreground: oklch(1.0000 0 0);
    --sidebar-accent: oklch(0.3587 0.0650 267.9772);
    --sidebar-accent-foreground: oklch(0.9636 0.0176 253.3378);
    --sidebar-border: oklch(0.4100 0.0316 268.3338);
    --sidebar-ring: oklch(0.6770 0.1511 36.8636);
    --font-sans: Outfit, sans-serif;
    --font-serif: Oxanium, sans-serif;
    --font-mono: JetBrains Mono, monospace;
    --radius: 0.45rem;
    --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.05);
    --shadow-xs: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.05);
    --shadow-sm: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.10), 0px 1px 2px -1px hsl(0 0% 10.1961% / 0.10);
    --shadow: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.10), 0px 1px 2px -1px hsl(0 0% 10.1961% / 0.10);
    --shadow-md: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.10), 0px 2px 4px -1px hsl(0 0% 10.1961% / 0.10);
    --shadow-lg: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.10), 0px 4px 6px -1px hsl(0 0% 10.1961% / 0.10);
    --shadow-xl: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.10), 0px 8px 10px -1px hsl(0 0% 10.1961% / 0.10);
    --shadow-2xl: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.25);
}

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);

    --font-sans: var(--font-sans);
    --font-mono: var(--font-mono);
    --font-serif: var(--font-serif);

    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);

    --shadow-2xs: var(--shadow-2xs);
    --shadow-xs: var(--shadow-xs);
    --shadow-sm: var(--shadow-sm);
    --shadow: var(--shadow);
    --shadow-md: var(--shadow-md);
    --shadow-lg: var(--shadow-lg);
    --shadow-xl: var(--shadow-xl);
    --shadow-2xl: var(--shadow-2xl);
}

@layer base {
    * {
        @apply border-border outline-ring/50;
    }

    body {
        @apply bg-background text-foreground;
        letter-spacing: var(--tracking-normal);
    }
}

@layer components {

    /* Landing page specific styles */
    .gradient-text {
        @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
    }

    .gradient-border {
        @apply border border-transparent bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-border;
    }

    /* Code syntax highlighting for demos */
    .code-keyword {
        @apply text-purple-400;
    }

    .code-string {
        @apply text-green-400;
    }

    .code-function {
        @apply text-yellow-400;
    }

    .code-variable {
        @apply text-blue-400;
    }

    .code-comment {
        @apply text-slate-400;
    }

    /* Animated background for hero section */
    .hero-bg {
        background: linear-gradient(135deg,
                hsl(var(--background)) 0%,
                hsl(var(--muted)) 50%,
                hsl(var(--background)) 100%);
    }

    /* Glassmorphism effect for cards */
    .glass-card {
        @apply bg-white/10 backdrop-blur-sm border border-white/20;
    }

    /* Hover animations */
    .hover-lift {
        @apply transition-transform duration-300 hover:-translate-y-1;
    }

    .hover-glow {
        @apply transition-shadow duration-300 hover:shadow-lg hover:shadow-blue-500/25;
    }

    /* Grid and dot patterns */
    .bg-grid-slate-100\/50 {
        background-image: url("data:image/svg+xml,%3csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='%23f1f5f9' fill-opacity='0.5' fill-rule='evenodd'%3e%3cpath d='m0 40 40-40h-40v40zm40 0v-40h-40l40 40z'/%3e%3c/g%3e%3c/svg%3e");
    }

    .bg-grid-slate-800\/50 {
        background-image: url("data:image/svg+xml,%3csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='%231e293b' fill-opacity='0.5' fill-rule='evenodd'%3e%3cpath d='m0 40 40-40h-40v40zm40 0v-40h-40l40 40z'/%3e%3c/g%3e%3c/svg%3e");
    }

    .bg-dot-slate-100\/50 {
        background-image: radial-gradient(circle, #f1f5f9 1px, transparent 1px);
        background-size: 20px 20px;
        opacity: 0.5;
    }

    .bg-dot-slate-800\/50 {
        background-image: radial-gradient(circle, #1e293b 1px, transparent 1px);
        background-size: 20px 20px;
        opacity: 0.5;
    }
}

@layer utilities {
    /* Custom animations */
    @keyframes float {
        0%, 100% {
            transform: translateY(0px) rotate(0deg);
        }
        33% {
            transform: translateY(-10px) rotate(1deg);
        }
        66% {
            transform: translateY(5px) rotate(-1deg);
        }
    }

    @keyframes glow {
        0%, 100% {
            opacity: 0.5;
            transform: scale(1);
        }
        50% {
            opacity: 1;
            transform: scale(1.05);
        }
    }

    @keyframes shimmer {
        0% {
            background-position: -200% 0;
        }
        100% {
            background-position: 200% 0;
        }
    }

    @keyframes pulse-glow {
        0%, 100% {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
        }
        50% {
            box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
        }
    }

    .animate-float {
        animation: float 6s ease-in-out infinite;
    }

    .animate-glow {
        animation: glow 2s ease-in-out infinite;
    }

    .animate-shimmer {
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        background-size: 200% 100%;
        animation: shimmer 2s infinite;
    }

    .animate-pulse-glow {
        animation: pulse-glow 2s ease-in-out infinite;
    }

    /* Gradient text utilities */
    .text-gradient-blue-purple {
        @apply bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent;
    }

    .text-gradient-cyan-blue {
        @apply bg-gradient-to-r from-cyan-400 to-blue-600 bg-clip-text text-transparent;
    }

    /* Background utilities */
    .bg-gradient-radial {
        background: radial-gradient(ellipse at center, var(--tw-gradient-stops));
    }

    .bg-gradient-conic {
        background: conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops));
    }
}