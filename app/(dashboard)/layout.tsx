import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { DashboardLayout as Layout } from "@/components/dashboard/layout/dashboard-layout";
import { getUserOrganizations } from "@/lib/data/organization";
import { db } from "@/lib/db";

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await auth();

  if (!session?.user) {
    redirect("/auth/login");
  }

  // Get user's organizations
  const organizations = await getUserOrganizations(session.user.id);

  // Prioritize organization with Pyodide projects (for showcasing seeded projects)
  let currentOrganization = organizations[0]; // Default to first

  // Check if any organization has Pyodide projects and prioritize it
  for (const org of organizations) {
    const projects = await db.project.findMany({
      where: { organizationId: org.id },
      include: {
        workspaces: {
          where: { type: 'PYODIDE' },
          select: { id: true }
        }
      },
      take: 1 // Just check if any exist
    });

    if (projects.some(p => p.workspaces.length > 0)) {
      currentOrganization = org;
      break; // Use the first org with Pyodide projects
    }
  }

  return (
    <Layout
      user={session.user}
      organizations={organizations.map(org => ({
        id: org.id,
        name: org.name,
        slug: org.slug,
        role: org.role as "OWNER" | "ADMIN" | "MEMBER"
      }))}
      currentOrganization={currentOrganization ? {
        id: currentOrganization.id,
        name: currentOrganization.name,
        slug: currentOrganization.slug,
        role: currentOrganization.role as "OWNER" | "ADMIN" | "MEMBER"
      } : undefined}
    >
      {children}
    </Layout>
  );
}