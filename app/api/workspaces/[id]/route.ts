import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { updateWorkspaceSchema } from "@/lib/validations/workspace";
import { 
  getWorkspaceById, 
  updateWorkspace, 
  deleteWorkspace 
} from "@/lib/data/workspace";
import { getUserOrganizationRole } from "@/lib/data/organization";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const workspace = await getWorkspaceById(params.id);
    if (!workspace) {
      return NextResponse.json({ error: "Workspace not found" }, { status: 404 });
    }

    // Check if user has access to this workspace's organization
    const userRole = await getUserOrganizationRole(
      session.user.id, 
      workspace.project.organizationId
    );
    if (!userRole) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    return NextResponse.json(workspace);
  } catch (error) {
    console.error("Error fetching workspace:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // First get the workspace to check permissions
    const existingWorkspace = await getWorkspaceById(params.id);
    if (!existingWorkspace) {
      return NextResponse.json({ error: "Workspace not found" }, { status: 404 });
    }

    // Check if user has permission to update this workspace
    const userRole = await getUserOrganizationRole(
      session.user.id, 
      existingWorkspace.project.organizationId
    );
    if (!userRole || (userRole !== "OWNER" && userRole !== "ADMIN")) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = updateWorkspaceSchema.parse(body);

    const workspace = await updateWorkspace(params.id, validatedData);
    return NextResponse.json(workspace);
  } catch (error) {
    console.error("Error updating workspace:", error);
    if (error instanceof Error && error.message.includes("validation")) {
      return NextResponse.json({ error: "Invalid input data" }, { status: 400 });
    }
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // First get the workspace to check permissions
    const existingWorkspace = await getWorkspaceById(params.id);
    if (!existingWorkspace) {
      return NextResponse.json({ error: "Workspace not found" }, { status: 404 });
    }

    // Check if user has permission to delete this workspace
    const userRole = await getUserOrganizationRole(
      session.user.id, 
      existingWorkspace.project.organizationId
    );
    if (!userRole || (userRole !== "OWNER" && userRole !== "ADMIN")) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
    }

    await deleteWorkspace(params.id);
    return NextResponse.json({ message: "Workspace deleted successfully" });
  } catch (error) {
    console.error("Error deleting workspace:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
