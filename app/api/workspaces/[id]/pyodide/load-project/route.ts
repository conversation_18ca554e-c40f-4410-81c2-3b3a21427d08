import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/lib/db';
import { PyodideProjectLoader } from '@/lib/services/pyodide-project-loader';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { 
      overwriteExisting = false, 
      createIndexes = true, 
      includeReadme = true 
    } = body;

    // Get workspace and verify access
    const workspace = await db.workspace.findUnique({
      where: { id: params.id },
      include: {
        project: {
          select: { 
            id: true,
            name: true,
            description: true,
            organizationId: true 
          }
        }
      }
    });

    if (!workspace) {
      return NextResponse.json({ error: "Workspace not found" }, { status: 404 });
    }

    // Verify workspace is Pyodide type
    if (workspace.type !== 'PYODIDE') {
      return NextResponse.json({ error: "Not a Pyodide workspace" }, { status: 400 });
    }

    // Verify workspace has a project
    if (!workspace.project) {
      return NextResponse.json({ error: "Workspace is not associated with a project" }, { status: 400 });
    }

    // Use the project loader service
    const projectLoader = new PyodideProjectLoader(workspace.id);
    const loadResult = await projectLoader.loadProjectFiles(workspace.project.id, {
      overwriteExisting,
      createIndexes,
      includeReadme
    });

    if (!loadResult.success) {
      return NextResponse.json({ 
        error: `Failed to load project files: ${loadResult.error}` 
      }, { status: 500 });
    }

    // Return detailed information about loaded files
    return NextResponse.json({
      success: true,
      message: `Successfully loaded ${loadResult.filesLoaded.length} files from project`,
      project: {
        id: workspace.project.id,
        name: loadResult.projectName
      },
      filesLoaded: loadResult.filesLoaded.map(file => ({
        id: file.id,
        name: file.name,
        path: file.path,
        type: file.type,
        size: file.size,
        isDirectory: file.isDirectory,
        source: file.source
      })),
      stats: {
        totalFiles: loadResult.filesLoaded.length,
        pythonFiles: loadResult.filesLoaded.filter(f => f.type === 'PYTHON').length,
        directories: loadResult.filesLoaded.filter(f => f.isDirectory).length,
        templateFiles: loadResult.filesLoaded.filter(f => f.source === 'template').length,
        starterFiles: loadResult.filesLoaded.filter(f => f.source === 'starter').length
      }
    });

  } catch (error) {
    console.error('Error loading project files:', error);
    return NextResponse.json({ 
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// GET endpoint to check project loading status
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get workspace and verify access
    const workspace = await db.workspace.findUnique({
      where: { id: params.id },
      include: {
        project: {
          select: { 
            id: true,
            name: true,
            description: true 
          }
        },
        files: {
          select: {
            id: true,
            path: true,
            name: true,
            type: true,
            isDirectory: true,
            metadata: true,
            createdAt: true
          }
        }
      }
    });

    if (!workspace) {
      return NextResponse.json({ error: "Workspace not found" }, { status: 404 });
    }

    // Verify workspace is Pyodide type
    if (workspace.type !== 'PYODIDE') {
      return NextResponse.json({ error: "Not a Pyodide workspace" }, { status: 400 });
    }

    const hasProject = !!workspace.project;
    const hasFiles = workspace.files.length > 0;
    const hasProjectFiles = workspace.files.some(file => 
      file.metadata && 
      (file.metadata as any).loadedFromProject === true
    );

    return NextResponse.json({
      workspace: {
        id: workspace.id,
        hasProject,
        hasFiles,
        hasProjectFiles,
        fileCount: workspace.files.length
      },
      project: workspace.project ? {
        id: workspace.project.id,
        name: workspace.project.name,
        description: workspace.project.description
      } : null,
      files: workspace.files.map(file => ({
        id: file.id,
        name: file.name,
        path: file.path,
        type: file.type,
        isDirectory: file.isDirectory,
        isProjectFile: file.metadata && (file.metadata as any).loadedFromProject === true,
        source: file.metadata && (file.metadata as any).source,
        createdAt: file.createdAt
      }))
    });

  } catch (error) {
    console.error('Error checking project loading status:', error);
    return NextResponse.json({ 
      error: "Internal server error" 
    }, { status: 500 });
  }
}
