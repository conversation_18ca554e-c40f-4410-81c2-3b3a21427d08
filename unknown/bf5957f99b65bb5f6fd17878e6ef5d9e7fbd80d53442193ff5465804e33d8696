# VibeKraft Production Docker Compose
# Complete infrastructure setup for AWS Lightsail deployment

version: '3.8'

networks:
  vibekraft-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

services:
  # =============================================================================
  # DATABASE SERVICES
  # =============================================================================
  postgres:
    image: postgres:16-alpine
    container_name: vibekraft-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-vibekraft}
      POSTGRES_USER: ${POSTGRES_USER:-vibekraft}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - vibekraft-net
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-vibekraft} -d ${POSTGRES_DB:-vibekraft}"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    container_name: vibekraft-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - vibekraft-net
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # STORAGE SERVICES
  # =============================================================================
  minio:
    image: minio/minio:latest
    container_name: vibekraft-minio
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ACCESS_KEY}
      MINIO_ROOT_PASSWORD: ${MINIO_SECRET_KEY}
      MINIO_BROWSER_REDIRECT_URL: https://storage.${DOMAIN}
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      - vibekraft-net
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # MONITORING SERVICES
  # =============================================================================
  prometheus:
    image: prom/prometheus:latest
    container_name: vibekraft-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - vibekraft-net
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3

  grafana:
    image: grafana/grafana:latest
    container_name: vibekraft-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_ADMIN_PASSWORD}
      GF_USERS_ALLOW_SIGN_UP: false
      GF_SERVER_ROOT_URL: https://grafana.${DOMAIN}
      GF_DATABASE_TYPE: postgres
      GF_DATABASE_HOST: postgres:5432
      GF_DATABASE_NAME: grafana
      GF_DATABASE_USER: ${POSTGRES_USER:-vibekraft}
      GF_DATABASE_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./config/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    ports:
      - "3001:3000"
    networks:
      - vibekraft-net
    depends_on:
      - postgres
      - prometheus
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # APPLICATION SERVICES
  # =============================================================================
  vibekraft-web:
    build:
      context: .
      dockerfile: Dockerfile
      target: runner
    container_name: vibekraft-web
    restart: unless-stopped
    environment:
      # Database
      DATABASE_URL: postgresql://${POSTGRES_USER:-vibekraft}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-vibekraft}
      
      # Redis
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379
      
      # MinIO
      MINIO_ENDPOINT: http://minio:9000
      MINIO_ACCESS_KEY: ${MINIO_ACCESS_KEY}
      MINIO_SECRET_KEY: ${MINIO_SECRET_KEY}
      MINIO_BUCKET_NAME: vibekraft-storage
      
      # API URLs
      API_URL: https://api.${DOMAIN}
      WEBSOCKET_URL: wss://ws.${DOMAIN}
      
      # Monitoring
      PROMETHEUS_URL: http://prometheus:9090
      GRAFANA_URL: http://grafana:3000
      GRAFANA_API_KEY: ${GRAFANA_API_KEY}
      
      # NextAuth
      NEXTAUTH_URL: https://${DOMAIN}
      NEXTAUTH_SECRET: ${NEXTAUTH_SECRET}
      
      # OAuth
      GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID}
      GOOGLE_CLIENT_SECRET: ${GOOGLE_CLIENT_SECRET}
      GITHUB_CLIENT_ID: ${GITHUB_CLIENT_ID}
      GITHUB_CLIENT_SECRET: ${GITHUB_CLIENT_SECRET}
      
      # Infrastructure
      INFRASTRUCTURE_JWT_SECRET: ${INFRASTRUCTURE_JWT_SECRET}
      ADMIN_API_KEY: ${ADMIN_API_KEY}
      WEBHOOK_SECRET: ${WEBHOOK_SECRET}
      
      # Feature Flags
      ENABLE_FIRECRACKER: true
      ENABLE_WEBVM: true
      ENABLE_AI_FEATURES: true
      ENABLE_METRICS_DASHBOARD: true
      ENABLE_STORAGE_BROWSER: true
      
      # Node Environment
      NODE_ENV: production
      PORT: 3000
    ports:
      - "3000:3000"
    networks:
      - vibekraft-net
    depends_on:
      - postgres
      - redis
      - minio
    healthcheck:
      test: ["CMD", "node", "scripts/healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # =============================================================================
  # REVERSE PROXY
  # =============================================================================
  nginx:
    image: nginx:alpine
    container_name: vibekraft-nginx
    restart: unless-stopped
    volumes:
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/nginx/sites-enabled:/etc/nginx/sites-enabled:ro
      - ./ssl:/etc/nginx/ssl:ro
    ports:
      - "80:80"
      - "443:443"
    networks:
      - vibekraft-net
    depends_on:
      - vibekraft-web
      - minio
      - grafana
      - prometheus
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # MONITORING EXPORTERS
  # =============================================================================
  node-exporter:
    image: prom/node-exporter:latest
    container_name: vibekraft-node-exporter
    restart: unless-stopped
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    ports:
      - "9100:9100"
    networks:
      - vibekraft-net

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: vibekraft-cadvisor
    restart: unless-stopped
    privileged: true
    devices:
      - /dev/kmsg:/dev/kmsg
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker:/var/lib/docker:ro
      - /cgroup:/cgroup:ro
    ports:
      - "8080:8080"
    networks:
      - vibekraft-net
