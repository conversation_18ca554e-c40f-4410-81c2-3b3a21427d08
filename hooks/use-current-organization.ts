import { useOrganization } from "@/components/providers/organization-provider";

/**
 * Hook to get the current organization ID
 * Returns null if no organization is selected
 */
export function useCurrentOrganizationId(): string | null {
  const { currentOrganization } = useOrganization();
  return currentOrganization?.id || null;
}

/**
 * Hook to get the current organization
 * Returns null if no organization is selected
 */
export function useCurrentOrganization() {
  const { currentOrganization } = useOrganization();
  return currentOrganization;
}

/**
 * Hook to check if an organization is currently selected
 */
export function useHasOrganization(): boolean {
  const { currentOrganization } = useOrganization();
  return !!currentOrganization;
}
