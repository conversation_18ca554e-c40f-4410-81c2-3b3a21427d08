# VibeKraft Prometheus Configuration
# Monitoring configuration for infrastructure services

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'vibekraft'
    environment: 'production'

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # Node Exporter (System metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s
    metrics_path: /metrics

  # cAdvisor (Container metrics)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 15s
    metrics_path: /metrics

  # VibeKraft Application
  - job_name: 'vibekraft-web'
    static_configs:
      - targets: ['vibekraft-web:3000']
    scrape_interval: 30s
    metrics_path: /api/metrics
    scrape_timeout: 10s

  # PostgreSQL
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s
    metrics_path: /metrics

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
    metrics_path: /metrics

  # MinIO
  - job_name: 'minio'
    static_configs:
      - targets: ['minio:9000']
    scrape_interval: 30s
    metrics_path: /minio/v2/metrics/cluster
    bearer_token_file: /etc/prometheus/minio-token

  # Nginx
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
    scrape_interval: 30s
    metrics_path: /nginx_status

  # Infrastructure Services Health Check
  - job_name: 'infrastructure-health'
    static_configs:
      - targets: ['vibekraft-web:3000']
    scrape_interval: 60s
    metrics_path: /api/infrastructure/health/metrics
    scrape_timeout: 30s

  # WebVM Instances (dynamic discovery)
  - job_name: 'webvm-instances'
    http_sd_configs:
      - url: http://vibekraft-web:3000/api/infrastructure/firecracker/discovery
        refresh_interval: 60s
    scrape_interval: 30s
    metrics_path: /metrics

  # Docker Containers (dynamic discovery)
  - job_name: 'docker-containers'
    http_sd_configs:
      - url: http://vibekraft-web:3000/api/infrastructure/docker/discovery
        refresh_interval: 60s
    scrape_interval: 30s
    metrics_path: /metrics

# Recording rules for aggregated metrics
recording_rules:
  - name: vibekraft.rules
    rules:
      # CPU usage aggregations
      - record: vibekraft:cpu_usage_percent
        expr: 100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)

      # Memory usage aggregations
      - record: vibekraft:memory_usage_percent
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100

      # Disk usage aggregations
      - record: vibekraft:disk_usage_percent
        expr: (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100

      # Network I/O aggregations
      - record: vibekraft:network_io_bytes_per_second
        expr: rate(node_network_receive_bytes_total[5m]) + rate(node_network_transmit_bytes_total[5m])

      # Application response time
      - record: vibekraft:http_request_duration_seconds_p95
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))

      # Container CPU usage
      - record: vibekraft:container_cpu_usage_percent
        expr: rate(container_cpu_usage_seconds_total[5m]) * 100

      # Container memory usage
      - record: vibekraft:container_memory_usage_percent
        expr: (container_memory_usage_bytes / container_spec_memory_limit_bytes) * 100

      # WebVM instance metrics
      - record: vibekraft:webvm_cpu_usage_percent
        expr: avg by (instance_id) (webvm_cpu_usage_percent)

      - record: vibekraft:webvm_memory_usage_percent
        expr: avg by (instance_id) (webvm_memory_usage_percent)

      # Storage metrics
      - record: vibekraft:storage_usage_bytes
        expr: sum by (bucket) (minio_bucket_usage_total_bytes)

      # Infrastructure health score
      - record: vibekraft:infrastructure_health_score
        expr: |
          (
            count(up{job=~"vibekraft-web|postgres|redis|minio"} == 1) /
            count(up{job=~"vibekraft-web|postgres|redis|minio"})
          ) * 100

# Alerting rules
alerting_rules:
  - name: vibekraft.alerts
    rules:
      # High CPU usage
      - alert: HighCPUUsage
        expr: vibekraft:cpu_usage_percent > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is above 80% for more than 5 minutes"

      # High memory usage
      - alert: HighMemoryUsage
        expr: vibekraft:memory_usage_percent > 85
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 85% for more than 5 minutes"

      # High disk usage
      - alert: HighDiskUsage
        expr: vibekraft:disk_usage_percent > 90
        for: 2m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "High disk usage detected"
          description: "Disk usage is above 90% for more than 2 minutes"

      # Service down
      - alert: ServiceDown
        expr: up{job=~"vibekraft-web|postgres|redis|minio"} == 0
        for: 1m
        labels:
          severity: critical
          service: "{{ $labels.job }}"
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "Service {{ $labels.job }} has been down for more than 1 minute"

      # High response time
      - alert: HighResponseTime
        expr: vibekraft:http_request_duration_seconds_p95 > 2
        for: 5m
        labels:
          severity: warning
          service: application
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is above 2 seconds for more than 5 minutes"

      # Container high CPU
      - alert: ContainerHighCPU
        expr: vibekraft:container_cpu_usage_percent > 80
        for: 5m
        labels:
          severity: warning
          service: container
        annotations:
          summary: "Container {{ $labels.name }} high CPU usage"
          description: "Container {{ $labels.name }} CPU usage is above 80% for more than 5 minutes"

      # Container high memory
      - alert: ContainerHighMemory
        expr: vibekraft:container_memory_usage_percent > 90
        for: 5m
        labels:
          severity: warning
          service: container
        annotations:
          summary: "Container {{ $labels.name }} high memory usage"
          description: "Container {{ $labels.name }} memory usage is above 90% for more than 5 minutes"

      # WebVM instance issues
      - alert: WebVMInstanceDown
        expr: up{job="webvm-instances"} == 0
        for: 2m
        labels:
          severity: warning
          service: webvm
        annotations:
          summary: "WebVM instance {{ $labels.instance_id }} is down"
          description: "WebVM instance {{ $labels.instance_id }} has been down for more than 2 minutes"

      # Storage quota exceeded
      - alert: StorageQuotaExceeded
        expr: (vibekraft:storage_usage_bytes / minio_bucket_quota_bytes) > 0.9
        for: 1m
        labels:
          severity: warning
          service: storage
        annotations:
          summary: "Storage quota exceeded for bucket {{ $labels.bucket }}"
          description: "Storage usage is above 90% of quota for bucket {{ $labels.bucket }}"

      # Infrastructure health degraded
      - alert: InfrastructureHealthDegraded
        expr: vibekraft:infrastructure_health_score < 75
        for: 3m
        labels:
          severity: critical
          service: infrastructure
        annotations:
          summary: "Infrastructure health degraded"
          description: "Infrastructure health score is below 75% for more than 3 minutes"
