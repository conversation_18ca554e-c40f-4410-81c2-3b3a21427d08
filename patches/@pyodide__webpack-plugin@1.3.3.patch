diff --git a/plugin.mjs b/plugin.mjs
index b5ac6b632509b8aff322efe223b31e2fd57982ab..a5b827e27438d798a10386c77786a6de2fcd33f8 100644
--- a/plugin.mjs
+++ b/plugin.mjs
@@ -307,7 +307,7 @@ class PyodidePlugin extends copy_webpack_plugin__WEBPACK_IMPORTED_MODULE_4__["de
     apply(compiler) {
         super.apply(compiler);
         compiler.hooks.compilation.tap(this.constructor.name, (compilation) => {
-            const compilationHooks = webpack__WEBPACK_IMPORTED_MODULE_5__["default"].NormalModule.getCompilationHooks(compilation);
+            const compilationHooks = compiler.webpack.NormalModule.getCompilationHooks(compilation);
             compilationHooks.beforeLoaders.tap(this.constructor.name, (loaders, normalModule) => {
                 const matches = normalModule.userRequest.match(/pyodide\.m?js$/);
                 if (matches) {
