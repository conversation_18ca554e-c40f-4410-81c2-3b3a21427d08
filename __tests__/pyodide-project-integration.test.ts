/**
 * Pyodide Project Integration Tests
 * Tests for project file loading into Pyodide workspaces
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { PyodideProjectLoader } from '@/lib/services/pyodide-project-loader';

// Mock dependencies
jest.mock('@/lib/db', () => ({
  db: {
    project: {
      findUnique: jest.fn(),
    },
    workspace: {
      findMany: jest.fn(),
    },
    workspaceFile: {
      findMany: jest.fn(),
      deleteMany: jest.fn(),
      create: jest.fn(),
    },
  },
}));

jest.mock('@/lib/workspace/services/file-storage', () => ({
  WorkspaceFileStorage: jest.fn().mockImplementation(() => ({})),
}));

jest.mock('@/lib/workspace/services/file-indexer', () => ({
  WorkspaceFileIndexer: jest.fn().mockImplementation(() => ({
    indexFile: jest.fn().mockResolvedValue(true),
  })),
}));

describe('PyodideProjectLoader', () => {
  let projectLoader: PyodideProjectLoader;
  const mockWorkspaceId = 'test-workspace-id';
  const mockProjectId = 'test-project-id';

  beforeEach(() => {
    projectLoader = new PyodideProjectLoader(mockWorkspaceId);
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('loadProjectFiles', () => {
    it('should successfully load project files when project exists', async () => {
      // Mock project data
      const mockProject = {
        id: mockProjectId,
        name: 'Test Project',
        description: 'A test project',
      };

      // Mock database calls
      const { db } = require('@/lib/db');
      db.project.findUnique.mockResolvedValue(mockProject);
      db.workspaceFile.findMany.mockResolvedValue([]); // No existing files
      db.workspace.findMany.mockResolvedValue([]); // No template files
      db.workspaceFile.create.mockResolvedValue({
        id: 'file-id',
        path: 'main.py',
        name: 'main.py',
        type: 'PYTHON',
        size: BigInt(100),
        isDirectory: false,
        content: 'print("Hello")',
      });

      const result = await projectLoader.loadProjectFiles(mockProjectId);

      expect(result.success).toBe(true);
      expect(result.filesLoaded).toHaveLength(2); // main.py + README.md
      expect(result.projectName).toBe('Test Project');
      expect(db.project.findUnique).toHaveBeenCalledWith({
        where: { id: mockProjectId },
        select: { id: true, name: true, description: true },
      });
    });

    it('should return error when project does not exist', async () => {
      const { db } = require('@/lib/db');
      db.project.findUnique.mockResolvedValue(null);

      const result = await projectLoader.loadProjectFiles(mockProjectId);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Project not found');
      expect(result.filesLoaded).toHaveLength(0);
    });

    it('should not overwrite existing files when overwriteExisting is false', async () => {
      const mockProject = {
        id: mockProjectId,
        name: 'Test Project',
        description: 'A test project',
      };

      const { db } = require('@/lib/db');
      db.project.findUnique.mockResolvedValue(mockProject);
      db.workspaceFile.findMany.mockResolvedValue([
        { id: 'existing-file', path: 'existing.py' }
      ]); // Existing files

      const result = await projectLoader.loadProjectFiles(mockProjectId, {
        overwriteExisting: false
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('already contains files');
      expect(db.workspaceFile.deleteMany).not.toHaveBeenCalled();
    });

    it('should overwrite existing files when overwriteExisting is true', async () => {
      const mockProject = {
        id: mockProjectId,
        name: 'Test Project',
        description: 'A test project',
      };

      const { db } = require('@/lib/db');
      db.project.findUnique.mockResolvedValue(mockProject);
      db.workspaceFile.findMany.mockResolvedValue([
        { id: 'existing-file', path: 'existing.py' }
      ]); // Existing files
      db.workspace.findMany.mockResolvedValue([]); // No template files
      db.workspaceFile.deleteMany.mockResolvedValue({ count: 1 });
      db.workspaceFile.create.mockResolvedValue({
        id: 'file-id',
        path: 'main.py',
        name: 'main.py',
        type: 'PYTHON',
        size: BigInt(100),
        isDirectory: false,
        content: 'print("Hello")',
      });

      const result = await projectLoader.loadProjectFiles(mockProjectId, {
        overwriteExisting: true
      });

      expect(result.success).toBe(true);
      expect(db.workspaceFile.deleteMany).toHaveBeenCalledWith({
        where: { workspaceId: mockWorkspaceId }
      });
    });

    it('should load template files when available', async () => {
      const mockProject = {
        id: mockProjectId,
        name: 'Test Project',
        description: 'A test project',
      };

      const mockTemplateFiles = [
        {
          id: 'template-file-1',
          path: 'template.py',
          name: 'template.py',
          type: 'PYTHON',
          content: 'print("Template")',
          isDirectory: false,
          mimeType: 'text/x-python',
          metadata: { createdBy: 'template' }
        }
      ];

      const { db } = require('@/lib/db');
      db.project.findUnique.mockResolvedValue(mockProject);
      db.workspaceFile.findMany.mockResolvedValue([]); // No existing files
      db.workspace.findMany.mockResolvedValue([
        { files: mockTemplateFiles }
      ]);
      db.workspaceFile.create.mockResolvedValue({
        id: 'new-file-id',
        path: 'template.py',
        name: 'template.py',
        type: 'PYTHON',
        size: BigInt(100),
        isDirectory: false,
        content: 'print("Template")',
      });

      const result = await projectLoader.loadProjectFiles(mockProjectId);

      expect(result.success).toBe(true);
      expect(result.filesLoaded.length).toBeGreaterThan(0);
      expect(result.filesLoaded.some(f => f.source === 'template')).toBe(true);
    });

    it('should create file indexes when createIndexes is true', async () => {
      const mockProject = {
        id: mockProjectId,
        name: 'Test Project',
        description: 'A test project',
      };

      const { db } = require('@/lib/db');
      const { WorkspaceFileIndexer } = require('@/lib/workspace/services/file-indexer');
      
      db.project.findUnique.mockResolvedValue(mockProject);
      db.workspaceFile.findMany.mockResolvedValue([]);
      db.workspace.findMany.mockResolvedValue([]);
      db.workspaceFile.create.mockResolvedValue({
        id: 'file-id',
        path: 'main.py',
        name: 'main.py',
        type: 'PYTHON',
        size: BigInt(100),
        isDirectory: false,
        content: 'print("Hello")',
      });

      const mockIndexer = new WorkspaceFileIndexer();
      
      const result = await projectLoader.loadProjectFiles(mockProjectId, {
        createIndexes: true
      });

      expect(result.success).toBe(true);
      // Note: In a real test, we would verify that indexFile was called
      // but since we're mocking the constructor, we can't easily access the instance
    });
  });

  describe('File creation', () => {
    it('should create starter files with correct content', async () => {
      const mockProject = {
        id: mockProjectId,
        name: 'My Python Project',
        description: 'A sample Python project',
      };

      const { db } = require('@/lib/db');
      db.project.findUnique.mockResolvedValue(mockProject);
      db.workspaceFile.findMany.mockResolvedValue([]);
      db.workspace.findMany.mockResolvedValue([]);
      
      let createdFiles: any[] = [];
      db.workspaceFile.create.mockImplementation((data: any) => {
        createdFiles.push(data.data);
        return Promise.resolve({
          id: `file-${createdFiles.length}`,
          ...data.data,
        });
      });

      const result = await projectLoader.loadProjectFiles(mockProjectId);

      expect(result.success).toBe(true);
      expect(createdFiles.length).toBe(2); // main.py + README.md
      
      const mainFile = createdFiles.find(f => f.name === 'main.py');
      expect(mainFile).toBeDefined();
      expect(mainFile.content).toContain('My Python Project');
      expect(mainFile.type).toBe('PYTHON');
      
      const readmeFile = createdFiles.find(f => f.name === 'README.md');
      expect(readmeFile).toBeDefined();
      expect(readmeFile.content).toContain('# My Python Project');
      expect(readmeFile.mimeType).toBe('text/markdown');
    });
  });

  describe('Error handling', () => {
    it('should handle database errors gracefully', async () => {
      const { db } = require('@/lib/db');
      db.project.findUnique.mockRejectedValue(new Error('Database connection failed'));

      const result = await projectLoader.loadProjectFiles(mockProjectId);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Database connection failed');
      expect(result.filesLoaded).toHaveLength(0);
    });

    it('should handle file creation errors gracefully', async () => {
      const mockProject = {
        id: mockProjectId,
        name: 'Test Project',
        description: 'A test project',
      };

      const { db } = require('@/lib/db');
      db.project.findUnique.mockResolvedValue(mockProject);
      db.workspaceFile.findMany.mockResolvedValue([]);
      db.workspace.findMany.mockResolvedValue([]);
      db.workspaceFile.create.mockRejectedValue(new Error('File creation failed'));

      const result = await projectLoader.loadProjectFiles(mockProjectId);

      expect(result.success).toBe(false);
      expect(result.error).toBe('File creation failed');
    });
  });
});
