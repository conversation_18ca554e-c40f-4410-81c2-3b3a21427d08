#!/usr/bin/env tsx

/**
 * Manual Integration Test for Pyodide Project File Loading
 * This script tests the complete flow of loading project files into Pyodide workspaces
 */

import { db } from '@/lib/db';
import { PyodideProjectLoader } from '@/lib/services/pyodide-project-loader';

async function createTestProject() {
  console.log('Creating test project...');
  
  // Create a test organization first
  const org = await db.organization.create({
    data: {
      name: 'Test Organization',
      slug: 'test-org-' + Date.now(),
    }
  });

  // Create a test project
  const project = await db.project.create({
    data: {
      name: 'Pyodide Test Project',
      description: 'A test project for Pyodide workspace integration',
      organizationId: org.id,
    }
  });

  console.log(`Created project: ${project.name} (${project.id})`);
  return { project, org };
}

async function createTestWorkspace(projectId: string) {
  console.log('Creating test workspace...');
  
  const workspace = await db.workspace.create({
    data: {
      name: 'Test Pyodide Workspace',
      description: 'Test workspace for project file loading',
      projectId: projectId,
      type: 'PYODIDE',
      status: 'INACTIVE',
      config: {
        testMode: true,
      }
    }
  });

  console.log(`Created workspace: ${workspace.name} (${workspace.id})`);
  return workspace;
}

async function createTemplateFiles(projectId: string) {
  console.log('Creating template workspace with files...');
  
  // Create a template workspace
  const templateWorkspace = await db.workspace.create({
    data: {
      name: 'Template Workspace',
      description: 'Template workspace with sample files',
      projectId: projectId,
      type: 'PYODIDE',
      status: 'INACTIVE',
    }
  });

  // Create template files
  const templateFiles = [
    {
      path: 'app.py',
      name: 'app.py',
      content: `# Template Application
import math

def calculate_area(radius):
    """Calculate the area of a circle"""
    return math.pi * radius ** 2

def main():
    print("Welcome to the Circle Area Calculator!")
    radius = float(input("Enter the radius: "))
    area = calculate_area(radius)
    print(f"The area of a circle with radius {radius} is {area:.2f}")

if __name__ == "__main__":
    main()
`,
      type: 'PYTHON',
      mimeType: 'text/x-python',
    },
    {
      path: 'utils.py',
      name: 'utils.py',
      content: `# Utility functions
def format_number(num, decimals=2):
    """Format a number to specified decimal places"""
    return round(num, decimals)

def validate_positive_number(value):
    """Validate that a value is a positive number"""
    try:
        num = float(value)
        return num > 0
    except ValueError:
        return False
`,
      type: 'PYTHON',
      mimeType: 'text/x-python',
    }
  ];

  for (const fileData of templateFiles) {
    await db.workspaceFile.create({
      data: {
        workspaceId: templateWorkspace.id,
        path: fileData.path,
        name: fileData.name,
        type: fileData.type,
        size: BigInt(Buffer.byteLength(fileData.content, 'utf8')),
        mimeType: fileData.mimeType,
        encoding: 'utf-8',
        content: fileData.content,
        hash: require('crypto').createHash('sha256').update(fileData.content).digest('hex'),
        isDirectory: false,
        permissions: {
          read: true,
          write: true,
          execute: true
        },
        metadata: {
          language: 'python',
          createdBy: 'template',
          description: `Template ${fileData.name}`,
        },
        version: 1,
        lastAccessedAt: new Date()
      }
    });
  }

  console.log(`Created ${templateFiles.length} template files in workspace ${templateWorkspace.id}`);
  return templateWorkspace;
}

async function testProjectFileLoading(workspaceId: string, projectId: string) {
  console.log('\n=== Testing Project File Loading ===');
  
  const projectLoader = new PyodideProjectLoader(workspaceId);
  
  // Test 1: Load project files
  console.log('Test 1: Loading project files...');
  const result = await projectLoader.loadProjectFiles(projectId, {
    overwriteExisting: false,
    createIndexes: true,
    includeReadme: true
  });

  console.log('Load result:', {
    success: result.success,
    filesLoaded: result.filesLoaded.length,
    projectName: result.projectName,
    error: result.error
  });

  if (result.success) {
    console.log('Loaded files:');
    result.filesLoaded.forEach(file => {
      console.log(`  - ${file.path} (${file.type}, ${file.source})`);
    });
  }

  // Test 2: Verify files in database
  console.log('\nTest 2: Verifying files in database...');
  const dbFiles = await db.workspaceFile.findMany({
    where: { workspaceId },
    select: {
      id: true,
      path: true,
      name: true,
      type: true,
      size: true,
      isDirectory: true,
      metadata: true
    }
  });

  console.log(`Found ${dbFiles.length} files in database:`);
  dbFiles.forEach(file => {
    const metadata = file.metadata as any;
    console.log(`  - ${file.path} (${file.type}) - ${metadata?.source || 'unknown source'}`);
  });

  // Test 3: Check file indexes
  console.log('\nTest 3: Checking file indexes...');
  const fileIndexes = await db.fileIndex.findMany({
    where: { workspaceId },
    select: {
      id: true,
      path: true,
      language: true,
      tokens: true
    }
  });

  console.log(`Found ${fileIndexes.length} file indexes:`);
  fileIndexes.forEach(index => {
    const tokens = index.tokens as any;
    console.log(`  - ${index.path} (${index.language}) - ${Array.isArray(tokens) ? tokens.length : 0} tokens`);
  });

  return result;
}

async function testOverwriteExisting(workspaceId: string, projectId: string) {
  console.log('\n=== Testing Overwrite Existing Files ===');
  
  const projectLoader = new PyodideProjectLoader(workspaceId);
  
  // Test overwrite with existing files
  const result = await projectLoader.loadProjectFiles(projectId, {
    overwriteExisting: true,
    createIndexes: true,
    includeReadme: true
  });

  console.log('Overwrite result:', {
    success: result.success,
    filesLoaded: result.filesLoaded.length,
    error: result.error
  });

  return result;
}

async function cleanup(projectId: string, orgId: string) {
  console.log('\n=== Cleaning up test data ===');
  
  // Delete workspaces (this will cascade delete files)
  await db.workspace.deleteMany({
    where: { projectId }
  });

  // Delete project
  await db.project.delete({
    where: { id: projectId }
  });

  // Delete organization
  await db.organization.delete({
    where: { id: orgId }
  });

  console.log('Cleanup completed');
}

async function main() {
  console.log('🚀 Starting Pyodide Project Integration Test\n');

  try {
    // Setup
    const { project, org } = await createTestProject();
    const workspace = await createTestWorkspace(project.id);
    await createTemplateFiles(project.id);

    // Run tests
    const loadResult = await testProjectFileLoading(workspace.id, project.id);
    
    if (loadResult.success) {
      await testOverwriteExisting(workspace.id, project.id);
    }

    // Cleanup
    await cleanup(project.id, org.id);

    console.log('\n✅ All tests completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  main().catch(console.error);
}

export { main as testPyodideProjectIntegration };
