#!/usr/bin/env tsx

import { db } from "../lib/db";

async function testPyodideWorkspaceLaunch() {
  try {
    console.log('🧪 Testing Pyodide Workspace Launch Functionality');
    console.log('==================================================');
    
    // Find Pyodide projects and their workspaces
    const pyodideProjects = await db.project.findMany({
      where: {
        workspaces: {
          some: {
            type: 'PYODIDE'
          }
        }
      },
      include: {
        workspaces: {
          where: {
            type: 'PYODIDE'
          }
        },
        organization: {
          select: {
            id: true,
            name: true
          }
        }
      },
      take: 5
    });
    
    console.log(`\n📁 Found ${pyodideProjects.length} Pyodide projects:`);
    
    pyodideProjects.forEach((project, index) => {
      console.log(`\n${index + 1}. ${project.name}`);
      console.log(`   Organization: ${project.organization.name}`);
      console.log(`   Project ID: ${project.id}`);
      
      project.workspaces.forEach((workspace, wsIndex) => {
        console.log(`   Workspace ${wsIndex + 1}:`);
        console.log(`     - Name: ${workspace.name}`);
        console.log(`     - ID: ${workspace.id}`);
        console.log(`     - Type: ${workspace.type}`);
        console.log(`     - Status: ${workspace.status}`);
        console.log(`     - Config: ${JSON.stringify(workspace.config, null, 2).slice(0, 200)}...`);
        
        // Generate expected launch URLs
        console.log(`   🚀 Expected Launch URLs:`);
        console.log(`     - Direct workspace: /workspace/pyodide?workspaceId=${workspace.id}`);
        console.log(`     - Via project: /workspace/pyodide?projectId=${project.id}`);
      });
    });
    
    // Test workspace launch logic simulation
    console.log('\n🔧 Testing Launch Logic:');
    console.log('========================');
    
    for (const project of pyodideProjects.slice(0, 3)) {
      console.log(`\n📋 Project: ${project.name}`);
      
      // Simulate the launch logic from ProjectsList
      const pyodideWorkspaces = project.workspaces.filter(w => w.type === 'PYODIDE');
      const allWorkspaces = project.workspaces;
      const targetWorkspaces = pyodideWorkspaces.length > 0 ? pyodideWorkspaces : allWorkspaces;
      
      if (targetWorkspaces.length > 0) {
        const workspace = targetWorkspaces[0];
        console.log(`   ✅ Found workspace: ${workspace.name} (${workspace.type})`);
        
        if (workspace.type === 'PYODIDE') {
          const launchUrl = `/workspace/pyodide?workspaceId=${workspace.id}`;
          console.log(`   🎯 Launch URL: ${launchUrl}`);
          console.log(`   📊 Status: ${workspace.status}`);
          console.log(`   🔧 Config keys: ${Object.keys(workspace.config as any || {}).join(', ')}`);
        }
      } else {
        console.log(`   ⚠️  No workspaces found - would create new one`);
        const createUrl = `/workspace/pyodide?projectId=${project.id}`;
        console.log(`   🎯 Create URL: ${createUrl}`);
      }
    }
    
    // Test template card logic
    console.log('\n🎨 Testing Template Card Logic:');
    console.log('===============================');
    
    for (const project of pyodideProjects.slice(0, 2)) {
      console.log(`\n📄 ${project.name}:`);
      
      const primaryWorkspace = project.workspaces[0];
      const pyodideWorkspace = project.workspaces.find(w => w.type === 'PYODIDE');
      const hasActiveWorkspace = pyodideWorkspace?.status === 'ACTIVE';
      
      console.log(`   Primary workspace: ${primaryWorkspace?.name || 'none'} (${primaryWorkspace?.type || 'none'})`);
      console.log(`   Pyodide workspace: ${pyodideWorkspace?.name || 'none'}`);
      console.log(`   Has active workspace: ${hasActiveWorkspace ? '✅ YES' : '❌ NO'}`);
      console.log(`   Button text would be: "${hasActiveWorkspace ? 'Open Workspace' : 'Launch Workspace'}"`);
      
      const config = primaryWorkspace?.config as any;
      if (config?.learningObjectives) {
        console.log(`   📚 Learning objectives: ${config.learningObjectives.length} items`);
        console.log(`   🎯 Difficulty: ${config.difficulty || 'not set'}`);
        console.log(`   ⏱️  Estimated time: ${config.estimatedTime || 'not set'}`);
      }
    }
    
    console.log('\n🎉 Test completed successfully!');
    console.log('\n💡 Next steps:');
    console.log('   1. Start the development server: npm run dev');
    console.log('   2. Navigate to: http://localhost:3000/dashboard/projects');
    console.log('   3. Click "Launch Workspace" on any Pyodide template card');
    console.log('   4. Verify it opens the correct Pyodide workspace interface');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await db.$disconnect();
  }
}

testPyodideWorkspaceLaunch();
