# Pyodide Project Integration

This document describes the implementation of database integration for Pyodide workspaces to fetch and load workspace files for selected projects.

## Overview

The Pyodide project integration allows workspaces to automatically load project template files and starter code when associated with a project. This provides a seamless experience for users starting new Python projects in the Pyodide environment.

## Architecture

### Components

1. **PyodideProjectLoader Service** (`lib/services/pyodide-project-loader.ts`)
   - Core service for loading project files into Pyodide workspaces
   - Handles template file discovery, starter file creation, and file indexing
   - Provides comprehensive error handling and logging

2. **Enhanced API Routes**
   - `/api/workspaces/[id]/pyodide/files` - Enhanced to support project file loading
   - `/api/workspaces/[id]/pyodide/load-project` - Dedicated endpoint for project file operations

3. **Updated PyodideFileSystem** (`components/pyodide/core/pyodide-filesystem.ts`)
   - Enhanced `loadFromDatabase()` method to automatically detect and load project files
   - Maintains backward compatibility with existing file loading patterns

4. **Enhanced PyodideWorkspaceContainer** (`components/pyodide/workspace/pyodide-workspace-container.tsx`)
   - Added project file loading UI controls
   - Automatic detection of project association
   - User feedback for file loading operations

## Features

### Automatic Project File Loading

When a Pyodide workspace is associated with a project:

1. **Template File Discovery**: The system searches for existing template files from other workspaces in the same project
2. **Starter File Creation**: If no templates exist, creates basic Python starter files
3. **README Generation**: Automatically creates project documentation
4. **File Indexing**: All loaded files are indexed for search functionality

### File Loading Options

```typescript
interface ProjectFileLoadOptions {
  overwriteExisting?: boolean;  // Replace existing files
  createIndexes?: boolean;      // Create search indexes
  includeReadme?: boolean;      // Generate README file
}
```

### File Sources

Files are categorized by source:
- `template`: Files copied from project templates
- `starter`: System-generated starter files
- `existing`: Previously created files

## Database Schema

The implementation uses existing database models:

- **WorkspaceFile**: Stores file content and metadata
- **FileIndex**: Provides search indexing for files
- **Workspace**: Links workspaces to projects
- **Project**: Contains project information

### File Metadata

Project files include additional metadata:

```json
{
  "loadedFromProject": true,
  "originalFileId": "source-file-id",
  "loadedAt": "2024-01-01T00:00:00Z",
  "source": "template|starter|existing",
  "createdBy": "template|system|user"
}
```

## API Endpoints

### GET /api/workspaces/[id]/pyodide/files

Enhanced to support project file loading:

**Query Parameters:**
- `path`: File path filter
- `includeContent`: Include file content in response
- `loadProjectFiles`: Trigger project file loading if workspace is empty

**Response:**
```json
{
  "files": [...],
  "projectLoaded": boolean,
  "projectName": string,
  "filesLoadedCount": number
}
```

### POST /api/workspaces/[id]/pyodide/load-project

Dedicated endpoint for project file loading:

**Request Body:**
```json
{
  "overwriteExisting": false,
  "createIndexes": true,
  "includeReadme": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully loaded N files from project",
  "project": {
    "id": "project-id",
    "name": "Project Name"
  },
  "filesLoaded": [...],
  "stats": {
    "totalFiles": 3,
    "pythonFiles": 2,
    "directories": 0,
    "templateFiles": 2,
    "starterFiles": 1
  }
}
```

### GET /api/workspaces/[id]/pyodide/load-project

Check project loading status:

**Response:**
```json
{
  "workspace": {
    "id": "workspace-id",
    "hasProject": true,
    "hasFiles": true,
    "hasProjectFiles": true,
    "fileCount": 3
  },
  "project": {
    "id": "project-id",
    "name": "Project Name",
    "description": "Project description"
  },
  "files": [...]
}
```

## Usage

### Automatic Loading

Project files are automatically loaded when:
1. A Pyodide workspace is associated with a project
2. The workspace has no existing files
3. The PyodideFileSystem initializes

### Manual Loading

Users can manually trigger project file loading:
1. Click the "Load Project" button in the workspace header
2. Use the dedicated API endpoint
3. Call the PyodideProjectLoader service directly

### Code Example

```typescript
import { PyodideProjectLoader } from '@/lib/services/pyodide-project-loader';

const loader = new PyodideProjectLoader(workspaceId);
const result = await loader.loadProjectFiles(projectId, {
  overwriteExisting: false,
  createIndexes: true,
  includeReadme: true
});

if (result.success) {
  console.log(`Loaded ${result.filesLoaded.length} files`);
} else {
  console.error(`Failed to load files: ${result.error}`);
}
```

## File Types and Templates

### Starter Files

When no template files exist, the system creates:

1. **main.py**: Main Python application file with project-specific content
2. **README.md**: Project documentation with getting started guide

### Template Files

Template files are discovered from:
1. Other workspaces in the same project
2. Files marked with `createdBy: 'template'` metadata
3. Files with `source: 'template'` metadata

## Error Handling

The implementation includes comprehensive error handling:

- **Project Not Found**: Returns appropriate error message
- **Database Errors**: Graceful degradation with logging
- **File Creation Failures**: Partial success reporting
- **Indexing Failures**: Non-blocking warnings

## Testing

### Unit Tests

Run the unit tests:
```bash
npm test __tests__/pyodide-project-integration.test.ts
```

### Integration Tests

Run the manual integration test:
```bash
npx tsx scripts/test-pyodide-project-integration.ts
```

## Performance Considerations

- **Lazy Loading**: Files are only loaded when needed
- **Batch Operations**: Multiple files are processed efficiently
- **Indexing**: File indexing runs asynchronously
- **Caching**: Database queries are optimized for performance

## Security

- **Authentication**: All endpoints require valid user sessions
- **Authorization**: Users can only access their organization's projects
- **Input Validation**: All inputs are validated and sanitized
- **File Size Limits**: Large files are handled appropriately

## Future Enhancements

1. **Project Templates**: Support for predefined project templates
2. **Git Integration**: Load files from Git repositories
3. **Collaborative Editing**: Real-time file synchronization
4. **Version Control**: File versioning and history
5. **Advanced Search**: Enhanced file search capabilities
