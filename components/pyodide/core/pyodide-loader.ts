/**
 * Pyodide Dynamic Loader
 * Handles dynamic loading of Pyodide to prevent build-time issues
 */

// Define our own Pyodide interface to avoid importing from the npm package
export interface PyodideInterface {
  runPython: (code: string) => any;
  runPythonAsync: (code: string) => Promise<any>;
  loadPackage: (packages: string | string[]) => Promise<void>;
  loadPackagesFromImports: (code: string) => Promise<void>;
  pyimport: (name: string) => any;
  globals: any;
  FS: any;
  version: string;
  loadedPackages: { [key: string]: string };
  isPyProxy: (obj: any) => boolean;
  registerJsModule: (name: string, module: any) => void;
  unregisterJsModule: (name: string) => void;
  toPy: (obj: any) => any;
  [key: string]: any;
}

export interface PyodideLoaderConfig {
  indexURL?: string;
  fullStdLib?: boolean;
  stdin?: (prompt: string) => string;
  stdout?: (text: string) => void;
  stderr?: (text: string) => void;
  jsglobals?: object;
}

class PyodideLoader {
  private static instance: PyodideLoader | null = null;
  private pyodidePromise: Promise<PyodideInterface> | null = null;
  private isLoading = false;

  static getInstance(): PyodideLoader {
    if (!PyodideLoader.instance) {
      PyodideLoader.instance = new PyodideLoader();
    }
    return PyodideLoader.instance;
  }

  /**
   * Load Pyodide dynamically
   */
  async loadPyodide(config: PyodideLoaderConfig = {}): Promise<PyodideInterface> {
    // Return existing promise if already loading
    if (this.pyodidePromise) {
      return this.pyodidePromise;
    }

    // Prevent multiple simultaneous loads
    if (this.isLoading) {
      throw new Error('Pyodide is already being loaded');
    }

    this.isLoading = true;

    try {
      this.pyodidePromise = this.loadPyodideInternal(config);
      const pyodide = await this.pyodidePromise;
      this.isLoading = false;
      return pyodide;
    } catch (error) {
      this.isLoading = false;
      this.pyodidePromise = null;
      throw error;
    }
  }

  /**
   * Internal method to load Pyodide
   */
  private async loadPyodideInternal(config: PyodideLoaderConfig): Promise<PyodideInterface> {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      throw new Error('Pyodide can only be loaded in a browser environment');
    }

    try {
      // Use CDN-only approach to avoid webpack build issues
      console.log('Loading Pyodide via CDN to avoid build-time webpack issues');
      const loadPyodide = await this.loadPyodideFromCDN();

      if (!loadPyodide) {
        throw new Error('loadPyodide function not found');
      }

      // Load Pyodide with configuration
      const pyodide = await loadPyodide({
        indexURL: config.indexURL || 'https://cdn.jsdelivr.net/pyodide/v0.28.0/full/',
        fullStdLib: config.fullStdLib ?? false,
        stdin: config.stdin ? (() => config.stdin!('')) : undefined,
        stdout: config.stdout,
        stderr: config.stderr,
        jsglobals: config.jsglobals || globalThis,
      });

      return pyodide;
    } catch (error) {
      console.error('Failed to load Pyodide:', error);
      throw new Error(`Failed to load Pyodide: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * CDN fallback loader using script tag injection
   */
  private async loadPyodideFromCDN(): Promise<any> {
    return new Promise((resolve, reject) => {
      // Check if Pyodide is already loaded globally
      if ((window as any).loadPyodide) {
        resolve((window as any).loadPyodide);
        return;
      }

      // Create script tag to load Pyodide from CDN
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/pyodide/v0.28.0/full/pyodide.js';
      script.async = true;

      script.onload = () => {
        if ((window as any).loadPyodide) {
          resolve((window as any).loadPyodide);
        } else {
          reject(new Error('loadPyodide not found after CDN script load'));
        }
      };

      script.onerror = () => {
        reject(new Error('Failed to load Pyodide from CDN'));
      };

      document.head.appendChild(script);
    });
  }

  /**
   * Check if Pyodide is already loaded
   */
  isLoaded(): boolean {
    return this.pyodidePromise !== null && !this.isLoading;
  }

  /**
   * Get loaded Pyodide instance (throws if not loaded)
   */
  async getPyodide(): Promise<PyodideInterface> {
    if (!this.pyodidePromise) {
      throw new Error('Pyodide not loaded. Call loadPyodide() first.');
    }
    return this.pyodidePromise;
  }

  /**
   * Reset the loader (for testing or reloading)
   */
  reset(): void {
    this.pyodidePromise = null;
    this.isLoading = false;
  }
}

// Export singleton instance
export const pyodideLoader = PyodideLoader.getInstance();
