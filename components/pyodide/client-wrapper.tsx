/**
 * Client-Side Wrapper for Pyodide Components
 * Ensures Pyodide components only render on the client side
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Loader2, Terminal } from 'lucide-react';

interface ClientWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
}

/**
 * Wrapper component that only renders children on the client side
 * This prevents SSR issues with Pyodide components
 */
export function ClientWrapper({ 
  children, 
  fallback,
  className 
}: ClientWrapperProps) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return fallback || (
      <div className={`flex items-center justify-center min-h-[400px] ${className || ''}`}>
        <div className="text-center space-y-4">
          <Terminal className="h-12 w-12 mx-auto text-primary animate-pulse" />
          <div>
            <h3 className="text-lg font-semibold">Loading Python Environment</h3>
            <p className="text-muted-foreground">
              Preparing your workspace...
            </p>
          </div>
          <Loader2 className="h-6 w-6 mx-auto animate-spin" />
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

/**
 * Higher-order component for wrapping Pyodide components
 */
export function withClientWrapper<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ReactNode
) {
  const WrappedComponent = (props: P) => (
    <ClientWrapper fallback={fallback}>
      <Component {...props} />
    </ClientWrapper>
  );

  WrappedComponent.displayName = `withClientWrapper(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}
