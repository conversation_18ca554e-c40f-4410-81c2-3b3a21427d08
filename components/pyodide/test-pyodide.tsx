'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { pyodideManager } from './core/pyodide-manager';

export function TestPyodide() {
  const [output, setOutput] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testPyodide = async () => {
    setLoading(true);
    setOutput('Loading Pyodide...\n');

    try {
      const pyodide = await pyodideManager.loadInstance({
        stdout: (text: string) => {
          setOutput(prev => prev + text + '\n');
        },
        stderr: (text: string) => {
          setOutput(prev => prev + 'ERROR: ' + text + '\n');
        }
      });

      if (pyodide) {
        setOutput(prev => prev + 'Pyodide loaded successfully!\n');
        
        // Test basic Python execution
        const result = pyodide.runPython(`
import sys
print(f"Python version: {sys.version}")
print("Hello from Pyodide!")

# Simple calculation
result = 2 + 2
print(f"2 + 2 = {result}")
result
        `);
        
        setOutput(prev => prev + `Python result: ${result}\n`);
      }
    } catch (error) {
      setOutput(prev => prev + `Error: ${error instanceof Error ? error.message : 'Unknown error'}\n`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>Pyodide Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button 
          onClick={testPyodide} 
          disabled={loading}
          className="w-full"
        >
          {loading ? 'Loading...' : 'Test Pyodide'}
        </Button>
        
        {output && (
          <div className="bg-black text-green-400 p-4 rounded font-mono text-sm whitespace-pre-wrap max-h-96 overflow-y-auto">
            {output}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
