"use client";

import { createContext, useContext, useState, useEffect, ReactNode } from "react";

interface Organization {
  id: string;
  name: string;
  slug: string;
  role: "OWNER" | "ADMIN" | "MEMBER";
}

interface OrganizationContextType {
  organizations: Organization[];
  currentOrganization: Organization | null;
  setCurrentOrganization: (org: Organization) => void;
  isLoading: boolean;
}

const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined);

interface OrganizationProviderProps {
  children: ReactNode;
  initialOrganizations?: Organization[];
  initialCurrentOrganization?: Organization;
}

export function OrganizationProvider({ 
  children, 
  initialOrganizations = [],
  initialCurrentOrganization 
}: OrganizationProviderProps) {
  const [organizations, setOrganizations] = useState<Organization[]>(initialOrganizations);
  const [currentOrganization, setCurrentOrganization] = useState<Organization | null>(
    initialCurrentOrganization || null
  );
  const [isLoading, setIsLoading] = useState(false);

  // Update organizations when initial data changes
  useEffect(() => {
    if (initialOrganizations.length > 0) {
      setOrganizations(initialOrganizations);
    }
  }, [initialOrganizations]);

  // Update current organization when initial data changes
  useEffect(() => {
    if (initialCurrentOrganization) {
      setCurrentOrganization(initialCurrentOrganization);
    }
  }, [initialCurrentOrganization]);

  const handleSetCurrentOrganization = (org: Organization) => {
    setCurrentOrganization(org);
    
    // Store in localStorage for persistence
    if (typeof window !== 'undefined') {
      localStorage.setItem('vk-current-organization', JSON.stringify(org));
    }
  };

  // Load from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined' && !initialCurrentOrganization) {
      const stored = localStorage.getItem('vk-current-organization');
      if (stored) {
        try {
          const org = JSON.parse(stored);
          // Verify the organization still exists in the list
          if (organizations.some(o => o.id === org.id)) {
            setCurrentOrganization(org);
          }
        } catch (error) {
          console.warn('Failed to parse stored organization:', error);
        }
      }
    }
  }, [organizations, initialCurrentOrganization]);

  const value: OrganizationContextType = {
    organizations,
    currentOrganization,
    setCurrentOrganization: handleSetCurrentOrganization,
    isLoading,
  };

  return (
    <OrganizationContext.Provider value={value}>
      {children}
    </OrganizationContext.Provider>
  );
}

export function useOrganization() {
  const context = useContext(OrganizationContext);
  if (context === undefined) {
    throw new Error('useOrganization must be used within an OrganizationProvider');
  }
  return context;
}
