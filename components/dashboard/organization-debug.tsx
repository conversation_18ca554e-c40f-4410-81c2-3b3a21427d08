"use client";

import { useOrganization } from "@/components/providers/organization-provider";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Building, Users, Crown, Shield } from "lucide-react";

export function OrganizationDebug() {
  const { organizations, currentOrganization, setCurrentOrganization } = useOrganization();

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "OWNER":
        return <Crown className="h-3 w-3 text-yellow-500" />;
      case "ADMIN":
        return <Shield className="h-3 w-3 text-blue-500" />;
      default:
        return <Users className="h-3 w-3 text-gray-500" />;
    }
  };

  const getRoleBadge = (role: string) => {
    switch (role) {
      case "OWNER":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Owner</Badge>;
      case "ADMIN":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Admin</Badge>;
      default:
        return <Badge variant="secondary">Member</Badge>;
    }
  };

  if (process.env.NODE_ENV !== 'development') {
    return null; // Only show in development
  }

  return (
    <Card className="border-dashed border-orange-200 bg-orange-50/50">
      <CardHeader>
        <CardTitle className="text-sm flex items-center gap-2">
          <Building className="h-4 w-4" />
          Organization Debug (Dev Only)
        </CardTitle>
        <CardDescription>
          Current organization context and switching functionality
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Organization */}
        <div>
          <h4 className="text-sm font-medium mb-2">Current Organization:</h4>
          {currentOrganization ? (
            <div className="flex items-center gap-2 p-2 rounded-lg bg-white border">
              <Building className="h-4 w-4" />
              <span className="font-medium">{currentOrganization.name}</span>
              {getRoleIcon(currentOrganization.role)}
              {getRoleBadge(currentOrganization.role)}
              <Badge variant="outline" className="text-xs">
                ID: {currentOrganization.id.slice(0, 8)}...
              </Badge>
            </div>
          ) : (
            <div className="text-sm text-muted-foreground p-2 rounded-lg bg-white border">
              No organization selected
            </div>
          )}
        </div>

        {/* Available Organizations */}
        <div>
          <h4 className="text-sm font-medium mb-2">Available Organizations ({organizations.length}):</h4>
          <div className="space-y-2">
            {organizations.map((org) => (
              <div 
                key={org.id} 
                className={`flex items-center justify-between p-2 rounded-lg border transition-colors ${
                  currentOrganization?.id === org.id 
                    ? 'bg-primary/10 border-primary/30' 
                    : 'bg-white hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center gap-2">
                  <Building className="h-4 w-4" />
                  <span className="font-medium">{org.name}</span>
                  {getRoleIcon(org.role)}
                  <Badge variant="outline" className="text-xs">
                    {org.slug}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  {getRoleBadge(org.role)}
                  {currentOrganization?.id !== org.id && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setCurrentOrganization(org)}
                      className="text-xs"
                    >
                      Switch
                    </Button>
                  )}
                  {currentOrganization?.id === org.id && (
                    <Badge className="bg-green-100 text-green-800 border-green-200">
                      Active
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Context Info */}
        <div className="text-xs text-muted-foreground space-y-1">
          <div>• Organization data is managed by OrganizationProvider</div>
          <div>• Current selection is persisted in localStorage</div>
          <div>• Projects list will update when organization changes</div>
          <div>• This debug panel only shows in development mode</div>
        </div>
      </CardContent>
    </Card>
  );
}
