"use client";

import { DashboardSidebar } from "./dashboard-sidebar";
import { DashboardHeader } from "./dashboard-header";
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";
import { OrganizationProvider } from "@/components/providers/organization-provider";

interface Organization {
  id: string;
  name: string;
  slug: string;
  role: "OWNER" | "ADMIN" | "MEMBER";
}

interface DashboardLayoutProps {
  children: React.ReactNode;
  user: {
    id?: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
    role?: string;
  };
  organizations?: Organization[];
  currentOrganization?: Organization;
}

export function DashboardLayout({
  children,
  user,
  organizations,
  currentOrganization
}: DashboardLayoutProps) {
  return (
    <OrganizationProvider
      initialOrganizations={organizations}
      initialCurrentOrganization={currentOrganization}
    >
      <SidebarProvider>
        <DashboardSidebar />
        <SidebarInset>
          <DashboardHeader
            user={user}
            organizations={organizations}
            currentOrganization={currentOrganization}
          />
          <main className="flex-1 overflow-auto">
            <div className="container mx-auto p-6">
              {children}
            </div>
          </main>
        </SidebarInset>
      </SidebarProvider>
    </OrganizationProvider>
  );
}
